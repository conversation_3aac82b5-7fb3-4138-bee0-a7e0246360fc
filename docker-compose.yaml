
version: '3.8'

services:
  open-webui-1:
    image: ghcr.io/open-webui/open-webui:main
    container_name: open-webui-1
    volumes:
      - /Users/<USER>/Workspace/openwebui/shared-data:/app/backend/data/
    ports:
      - "3000:8080"  # Change if needed
    restart: unless-stopped
    # networks:
    #   - restricted_network
    dns:
      - 127.0.0.1  # Point to localhost to block external DNS resolution

  open-webui-2:
    image: ghcr.io/open-webui/open-webui:main
    container_name: open-webui-2
    volumes:
      - /Users/<USER>/Workspace/openwebui/shared-data:/app/backend/data/
    ports:
      - "3001:8080"  # Change if needed
    restart: unless-stopped
    # networks:
    #   - restricted_network
    dns:
      - 127.0.0.1  # Point to localhost to block external DNS resolution

# volumes:
#   shared-data:

# networks:
#   restricted_network:
#     driver: bridge
